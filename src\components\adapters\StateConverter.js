/**
 * 状态转换器
 * 负责在不同图像编辑库之间转换状态数据
 */
class StateConverter {
  constructor() {
    // 支持的适配器类型
    this.supportedTypes = ['fabric', 'konva', 'cropper', 'jimp', 'tui'];
    
    // 状态字段映射表
    this.fieldMappings = {
      // 图像基本信息映射
      imageData: {
        fabric: 'currentImage',
        konva: 'currentImage', 
        cropper: 'originalImageData',
        jimp: 'originalImageData',
        tui: 'originalImageData'
      },
      
      // 变换信息映射
      transform: {
        fabric: ['left', 'top', 'scaleX', 'scaleY', 'angle', 'flipX', 'flipY'],
        konva: ['x', 'y', 'scaleX', 'scaleY', 'rotation'],
        cropper: ['cropData'],
        jimp: ['width', 'height'],
        tui: ['transform']
      },
      
      // 滤镜映射
      filters: {
        fabric: 'filters',
        konva: 'filters',
        cropper: null, // Cropper不支持滤镜
        jimp: 'appliedFilters',
        tui: 'filters'
      }
    };
  }

  /**
   * 转换状态格式
   * @param {Object} sourceState - 源状态
   * @param {string} fromType - 源适配器类型
   * @param {string} toType - 目标适配器类型
   * @returns {Promise<Object>} 转换后的状态
   */
  async convertState(sourceState, fromType, toType) {
    if (!this.supportedTypes.includes(fromType) || !this.supportedTypes.includes(toType)) {
      throw new Error(`Unsupported adapter type: ${fromType} -> ${toType}`);
    }

    if (fromType === toType) {
      return sourceState; // 相同类型直接返回
    }

    try {
      // 标准化源状态
      const normalizedState = await this._normalizeState(sourceState, fromType);
      
      // 转换为目标格式
      const targetState = await this._convertToTargetFormat(normalizedState, toType);
      
      return targetState;
      
    } catch (error) {
      console.error(`State conversion failed from ${fromType} to ${toType}:`, error);
      throw error;
    }
  }

  /**
   * 提取适配器的完整状态
   * @param {BaseImageEditorAdapter} adapter - 适配器实例
   * @param {string} adapterType - 适配器类型
   * @returns {Promise<Object>} 提取的状态
   */
  async extractAdapterState(adapter, adapterType) {
    const baseState = {
      adapterType,
      timestamp: Date.now(),
      imageData: null,
      transform: {},
      filters: [],
      objects: [],
      canvas: {},
      metadata: {}
    };

    try {
      // 获取基础信息
      baseState.imageData = adapter.getImageData();
      baseState.metadata = adapter.getPerformanceMetrics();

      // 根据适配器类型提取特定状态
      switch (adapterType) {
        case 'fabric':
          return await this._extractFabricState(adapter, baseState);
        case 'konva':
          return await this._extractKonvaState(adapter, baseState);
        case 'cropper':
          return await this._extractCropperState(adapter, baseState);
        case 'jimp':
          return await this._extractJimpState(adapter, baseState);
        case 'tui':
          return await this._extractTuiState(adapter, baseState);
        default:
          return baseState;
      }
    } catch (error) {
      console.error(`Failed to extract state from ${adapterType}:`, error);
      return baseState;
    }
  }

  /**
   * 将状态应用到目标适配器
   * @param {BaseImageEditorAdapter} adapter - 目标适配器
   * @param {Object} state - 要应用的状态
   * @param {string} adapterType - 适配器类型
   * @returns {Promise<void>}
   */
  async applyStateToAdapter(adapter, state, adapterType) {
    try {
      // 首先加载图像（如果有）
      if (state.imageData && state.imageData.src) {
        await adapter.loadImage(state.imageData.src);
      }

      // 根据适配器类型应用特定状态
      switch (adapterType) {
        case 'fabric':
          await this._applyFabricState(adapter, state);
          break;
        case 'konva':
          await this._applyKonvaState(adapter, state);
          break;
        case 'cropper':
          await this._applyCropperState(adapter, state);
          break;
        case 'jimp':
          await this._applyJimpState(adapter, state);
          break;
        case 'tui':
          await this._applyTuiState(adapter, state);
          break;
      }
    } catch (error) {
      console.error(`Failed to apply state to ${adapterType}:`, error);
      throw error;
    }
  }

  /**
   * 验证状态格式
   * @param {Object} state - 状态对象
   * @param {string} adapterType - 适配器类型
   * @returns {boolean} 是否有效
   */
  validateState(state, adapterType) {
    if (!state || typeof state !== 'object') {
      return false;
    }

    // 检查必需字段
    const requiredFields = ['adapterType', 'timestamp'];
    for (const field of requiredFields) {
      if (!(field in state)) {
        return false;
      }
    }

    // 检查适配器类型
    if (!this.supportedTypes.includes(adapterType)) {
      return false;
    }

    return true;
  }

  // ========== 私有方法 ==========

  /**
   * 标准化状态格式
   * @param {Object} state - 原始状态
   * @param {string} fromType - 源类型
   * @returns {Promise<Object>} 标准化状态
   * @private
   */
  async _normalizeState(state, fromType) {
    const normalized = {
      imageData: null,
      transform: {
        position: { x: 0, y: 0 },
        scale: { x: 1, y: 1 },
        rotation: 0,
        flip: { x: false, y: false }
      },
      filters: [],
      objects: [],
      canvas: {
        width: 800,
        height: 600,
        backgroundColor: '#ffffff'
      }
    };

    // 根据源类型标准化
    switch (fromType) {
      case 'fabric':
        return this._normalizeFabricState(state, normalized);
      case 'konva':
        return this._normalizeKonvaState(state, normalized);
      case 'cropper':
        return this._normalizeCropperState(state, normalized);
      case 'jimp':
        return this._normalizeJimpState(state, normalized);
      case 'tui':
        return this._normalizeTuiState(state, normalized);
      default:
        return normalized;
    }
  }

  /**
   * 转换为目标格式
   * @param {Object} normalizedState - 标准化状态
   * @param {string} toType - 目标类型
   * @returns {Promise<Object>} 目标格式状态
   * @private
   */
  async _convertToTargetFormat(normalizedState, toType) {
    switch (toType) {
      case 'fabric':
        return this._convertToFabricFormat(normalizedState);
      case 'konva':
        return this._convertToKonvaFormat(normalizedState);
      case 'cropper':
        return this._convertToCropperFormat(normalizedState);
      case 'jimp':
        return this._convertToJimpFormat(normalizedState);
      case 'tui':
        return this._convertToTuiFormat(normalizedState);
      default:
        return normalizedState;
    }
  }

  // ========== Fabric.js 相关方法 ==========

  /**
   * 提取Fabric状态
   * @param {FabricAdapter} adapter - Fabric适配器
   * @param {Object} baseState - 基础状态
   * @returns {Promise<Object>} Fabric状态
   * @private
   */
  async _extractFabricState(adapter, baseState) {
    if (!adapter.canvas) {
      return baseState;
    }

    try {
      // 获取画布对象
      const objects = adapter.canvas.getObjects();
      baseState.objects = objects.map(obj => ({
        type: obj.type,
        left: obj.left,
        top: obj.top,
        scaleX: obj.scaleX,
        scaleY: obj.scaleY,
        angle: obj.angle,
        flipX: obj.flipX,
        flipY: obj.flipY,
        opacity: obj.opacity,
        visible: obj.visible
      }));

      // 获取画布信息
      baseState.canvas = {
        width: adapter.canvas.getWidth(),
        height: adapter.canvas.getHeight(),
        backgroundColor: adapter.canvas.backgroundColor
      };

      // 获取当前图像的变换信息
      if (adapter.currentImage) {
        baseState.transform = {
          position: { x: adapter.currentImage.left, y: adapter.currentImage.top },
          scale: { x: adapter.currentImage.scaleX, y: adapter.currentImage.scaleY },
          rotation: adapter.currentImage.angle,
          flip: { x: adapter.currentImage.flipX, y: adapter.currentImage.flipY }
        };

        // 获取滤镜信息
        if (adapter.currentImage.filters) {
          baseState.filters = adapter.currentImage.filters.map(filter => ({
            type: filter.type,
            options: filter
          }));
        }
      }

      return baseState;
    } catch (error) {
      console.error('Failed to extract Fabric state:', error);
      return baseState;
    }
  }

  /**
   * 应用Fabric状态
   * @param {FabricAdapter} adapter - Fabric适配器
   * @param {Object} state - 状态数据
   * @returns {Promise<void>}
   * @private
   */
  async _applyFabricState(adapter, state) {
    if (!adapter.canvas || !state) {
      return;
    }

    try {
      // 应用画布设置
      if (state.canvas) {
        if (state.canvas.width && state.canvas.height) {
          adapter.canvas.setDimensions({
            width: state.canvas.width,
            height: state.canvas.height
          });
        }
        if (state.canvas.backgroundColor) {
          adapter.canvas.setBackgroundColor(state.canvas.backgroundColor);
        }
      }

      // 应用变换
      if (state.transform && adapter.currentImage) {
        if (state.transform.position) {
          adapter.currentImage.set({
            left: state.transform.position.x,
            top: state.transform.position.y
          });
        }
        if (state.transform.scale) {
          adapter.currentImage.set({
            scaleX: state.transform.scale.x,
            scaleY: state.transform.scale.y
          });
        }
        if (typeof state.transform.rotation === 'number') {
          adapter.currentImage.set('angle', state.transform.rotation);
        }
        if (state.transform.flip) {
          adapter.currentImage.set({
            flipX: state.transform.flip.x,
            flipY: state.transform.flip.y
          });
        }
      }

      // 应用滤镜
      if (state.filters && state.filters.length > 0 && adapter.currentImage) {
        for (const filterData of state.filters) {
          if (filterData.type && adapter._doApplyFilter) {
            await adapter._doApplyFilter(filterData.type, filterData.options || {});
          }
        }
      }

      // 重新渲染
      adapter.canvas.renderAll();
    } catch (error) {
      console.error('Failed to apply Fabric state:', error);
    }
  }

  // ========== Konva.js 相关方法 ==========

  /**
   * 提取Konva状态
   * @param {KonvaAdapter} adapter - Konva适配器
   * @param {Object} baseState - 基础状态
   * @returns {Promise<Object>} Konva状态
   * @private
   */
  async _extractKonvaState(adapter, baseState) {
    if (!adapter.stage || !adapter.layer) {
      return baseState;
    }

    try {
      // 获取舞台信息
      baseState.canvas = {
        width: adapter.stage.width(),
        height: adapter.stage.height()
      };

      // 获取当前图像的变换信息
      if (adapter.currentImage) {
        baseState.transform = {
          position: { x: adapter.currentImage.x(), y: adapter.currentImage.y() },
          scale: { x: adapter.currentImage.scaleX(), y: adapter.currentImage.scaleY() },
          rotation: adapter.currentImage.rotation(),
          flip: { x: adapter.currentImage.scaleX() < 0, y: adapter.currentImage.scaleY() < 0 }
        };
      }

      // 获取图层中的所有对象
      const children = adapter.layer.children || [];
      baseState.objects = children.map(child => ({
        type: child.className,
        x: child.x(),
        y: child.y(),
        scaleX: child.scaleX(),
        scaleY: child.scaleY(),
        rotation: child.rotation(),
        opacity: child.opacity(),
        visible: child.visible()
      }));

      return baseState;
    } catch (error) {
      console.error('Failed to extract Konva state:', error);
      return baseState;
    }
  }

  /**
   * 应用Konva状态
   * @param {KonvaAdapter} adapter - Konva适配器
   * @param {Object} state - 状态数据
   * @returns {Promise<void>}
   * @private
   */
  async _applyKonvaState(adapter, state) {
    if (!adapter.stage || !adapter.layer || !state) {
      return;
    }

    try {
      // 应用舞台设置
      if (state.canvas) {
        if (state.canvas.width && state.canvas.height) {
          adapter.stage.size({
            width: state.canvas.width,
            height: state.canvas.height
          });
        }
      }

      // 应用变换
      if (state.transform && adapter.currentImage) {
        if (state.transform.position) {
          adapter.currentImage.position({
            x: state.transform.position.x,
            y: state.transform.position.y
          });
        }
        if (state.transform.scale) {
          adapter.currentImage.scale({
            x: state.transform.scale.x,
            y: state.transform.scale.y
          });
        }
        if (typeof state.transform.rotation === 'number') {
          adapter.currentImage.rotation(state.transform.rotation);
        }
      }

      // 重新绘制
      adapter.layer.draw();
    } catch (error) {
      console.error('Failed to apply Konva state:', error);
    }
  }

  // ========== Cropper.js 相关方法 ==========

  /**
   * 提取Cropper状态
   * @param {CropperAdapter} adapter - Cropper适配器
   * @param {Object} baseState - 基础状态
   * @returns {Promise<Object>} Cropper状态
   * @private
   */
  async _extractCropperState(adapter, baseState) {
    if (!adapter.cropper) {
      return baseState;
    }

    try {
      // 获取裁剪数据
      const cropData = adapter.cropper.getData();
      baseState.transform.cropData = cropData;

      // 获取画布数据
      const canvasData = adapter.cropper.getCanvasData();
      baseState.canvas = {
        width: canvasData.naturalWidth,
        height: canvasData.naturalHeight
      };

      return baseState;
    } catch (error) {
      console.error('Failed to extract Cropper state:', error);
      return baseState;
    }
  }

  /**
   * 应用Cropper状态
   * @param {CropperAdapter} adapter - Cropper适配器
   * @param {Object} state - 状态数据
   * @returns {Promise<void>}
   * @private
   */
  async _applyCropperState(adapter, state) {
    if (!adapter.cropper || !state) {
      return;
    }

    try {
      // 应用裁剪数据
      if (state.transform && state.transform.cropData) {
        adapter.cropper.setData(state.transform.cropData);
      }
    } catch (error) {
      console.error('Failed to apply Cropper state:', error);
    }
  }

  // ========== Jimp 相关方法 ==========

  /**
   * 提取Jimp状态
   * @param {JimpAdapter} adapter - Jimp适配器
   * @param {Object} baseState - 基础状态
   * @returns {Promise<Object>} Jimp状态
   * @private
   */
  async _extractJimpState(adapter, baseState) {
    if (!adapter.jimpInstance) {
      return baseState;
    }

    try {
      // 获取图像尺寸
      baseState.canvas = {
        width: adapter.jimpInstance.getWidth(),
        height: adapter.jimpInstance.getHeight()
      };

      // Jimp的滤镜信息通常存储在适配器的appliedFilters中
      if (adapter.appliedFilters) {
        baseState.filters = [...adapter.appliedFilters];
      }

      return baseState;
    } catch (error) {
      console.error('Failed to extract Jimp state:', error);
      return baseState;
    }
  }

  /**
   * 应用Jimp状态
   * @param {JimpAdapter} adapter - Jimp适配器
   * @param {Object} state - 状态数据
   * @returns {Promise<void>}
   * @private
   */
  async _applyJimpState(adapter, state) {
    if (!adapter.jimpInstance || !state) {
      return;
    }

    try {
      // 应用尺寸变换
      if (state.canvas && state.canvas.width && state.canvas.height) {
        adapter.jimpInstance.resize(state.canvas.width, state.canvas.height);
      }

      // 应用滤镜
      if (state.filters && state.filters.length > 0) {
        for (const filterData of state.filters) {
          if (filterData.type && adapter._doApplyFilter) {
            await adapter._doApplyFilter(filterData.type, filterData.options || {});
          }
        }
      }

      // 重新渲染到Canvas
      if (adapter._renderToCanvas) {
        adapter._renderToCanvas();
      }
    } catch (error) {
      console.error('Failed to apply Jimp state:', error);
    }
  }

  // ========== TUI Image Editor 相关方法 ==========

  /**
   * 提取TUI状态
   * @param {TuiAdapter} adapter - TUI适配器
   * @param {Object} baseState - 基础状态
   * @returns {Promise<Object>} TUI状态
   * @private
   */
  async _extractTuiState(adapter, baseState) {
    if (!adapter.editor) {
      return baseState;
    }

    try {
      // 获取画布尺寸
      const canvasSize = adapter.editor.getCanvasSize();
      baseState.canvas = {
        width: canvasSize.width,
        height: canvasSize.height
      };

      // TUI Image Editor的状态通常通过其内部API获取
      // 这里我们主要保存图像数据
      return baseState;
    } catch (error) {
      console.error('Failed to extract TUI state:', error);
      return baseState;
    }
  }

  /**
   * 应用TUI状态
   * @param {TuiAdapter} adapter - TUI适配器
   * @param {Object} state - 状态数据
   * @returns {Promise<void>}
   * @private
   */
  async _applyTuiState(adapter, state) {
    if (!adapter.editor || !state) {
      return;
    }

    try {
      // TUI Image Editor的状态应用相对简单
      // 主要是确保图像已正确加载
      if (state.canvas && state.canvas.width && state.canvas.height) {
        adapter.editor.ui.resizeEditor({
          imageSize: {
            oldWidth: state.canvas.width,
            oldHeight: state.canvas.height,
            newWidth: state.canvas.width,
            newHeight: state.canvas.height
          }
        });
      }
    } catch (error) {
      console.error('Failed to apply TUI state:', error);
    }
  }

  // ========== 标准化方法 ==========

  /**
   * 标准化Fabric状态
   * @param {Object} state - Fabric状态
   * @param {Object} normalized - 标准化模板
   * @returns {Object} 标准化状态
   * @private
   */
  _normalizeFabricState(state, normalized) {
    if (state.transform) {
      normalized.transform.position = state.transform.position || { x: 0, y: 0 };
      normalized.transform.scale = state.transform.scale || { x: 1, y: 1 };
      normalized.transform.rotation = state.transform.rotation || 0;
      normalized.transform.flip = state.transform.flip || { x: false, y: false };
    }

    if (state.filters) {
      normalized.filters = [...state.filters];
    }

    if (state.objects) {
      normalized.objects = [...state.objects];
    }

    if (state.canvas) {
      normalized.canvas = { ...normalized.canvas, ...state.canvas };
    }

    normalized.imageData = state.imageData;
    return normalized;
  }

  /**
   * 标准化Konva状态
   * @param {Object} state - Konva状态
   * @param {Object} normalized - 标准化模板
   * @returns {Object} 标准化状态
   * @private
   */
  _normalizeKonvaState(state, normalized) {
    if (state.transform) {
      normalized.transform.position = state.transform.position || { x: 0, y: 0 };
      normalized.transform.scale = state.transform.scale || { x: 1, y: 1 };
      normalized.transform.rotation = state.transform.rotation || 0;
      normalized.transform.flip = state.transform.flip || { x: false, y: false };
    }

    if (state.objects) {
      normalized.objects = state.objects.map(obj => ({
        type: obj.type,
        position: { x: obj.x || 0, y: obj.y || 0 },
        scale: { x: obj.scaleX || 1, y: obj.scaleY || 1 },
        rotation: obj.rotation || 0,
        opacity: obj.opacity || 1,
        visible: obj.visible !== false
      }));
    }

    if (state.canvas) {
      normalized.canvas = { ...normalized.canvas, ...state.canvas };
    }

    normalized.imageData = state.imageData;
    return normalized;
  }

  /**
   * 标准化Cropper状态
   * @param {Object} state - Cropper状态
   * @param {Object} normalized - 标准化模板
   * @returns {Object} 标准化状态
   * @private
   */
  _normalizeCropperState(state, normalized) {
    if (state.transform && state.transform.cropData) {
      normalized.transform.cropData = state.transform.cropData;
    }

    if (state.canvas) {
      normalized.canvas = { ...normalized.canvas, ...state.canvas };
    }

    normalized.imageData = state.imageData;
    return normalized;
  }

  /**
   * 标准化Jimp状态
   * @param {Object} state - Jimp状态
   * @param {Object} normalized - 标准化模板
   * @returns {Object} 标准化状态
   * @private
   */
  _normalizeJimpState(state, normalized) {
    if (state.filters) {
      normalized.filters = [...state.filters];
    }

    if (state.canvas) {
      normalized.canvas = { ...normalized.canvas, ...state.canvas };
    }

    normalized.imageData = state.imageData;
    return normalized;
  }

  /**
   * 标准化TUI状态
   * @param {Object} state - TUI状态
   * @param {Object} normalized - 标准化模板
   * @returns {Object} 标准化状态
   * @private
   */
  _normalizeTuiState(state, normalized) {
    if (state.canvas) {
      normalized.canvas = { ...normalized.canvas, ...state.canvas };
    }

    normalized.imageData = state.imageData;
    return normalized;
  }

  // ========== 格式转换方法 ==========

  /**
   * 转换为Fabric格式
   * @param {Object} normalizedState - 标准化状态
   * @returns {Object} Fabric格式状态
   * @private
   */
  _convertToFabricFormat(normalizedState) {
    return {
      imageData: normalizedState.imageData,
      transform: {
        position: normalizedState.transform.position,
        scale: normalizedState.transform.scale,
        rotation: normalizedState.transform.rotation,
        flip: normalizedState.transform.flip
      },
      filters: normalizedState.filters,
      objects: normalizedState.objects,
      canvas: normalizedState.canvas
    };
  }

  /**
   * 转换为Konva格式
   * @param {Object} normalizedState - 标准化状态
   * @returns {Object} Konva格式状态
   * @private
   */
  _convertToKonvaFormat(normalizedState) {
    return {
      imageData: normalizedState.imageData,
      transform: {
        position: normalizedState.transform.position,
        scale: normalizedState.transform.scale,
        rotation: normalizedState.transform.rotation
      },
      objects: normalizedState.objects,
      canvas: normalizedState.canvas
    };
  }

  /**
   * 转换为Cropper格式
   * @param {Object} normalizedState - 标准化状态
   * @returns {Object} Cropper格式状态
   * @private
   */
  _convertToCropperFormat(normalizedState) {
    return {
      imageData: normalizedState.imageData,
      transform: {
        cropData: normalizedState.transform.cropData
      },
      canvas: normalizedState.canvas
    };
  }

  /**
   * 转换为Jimp格式
   * @param {Object} normalizedState - 标准化状态
   * @returns {Object} Jimp格式状态
   * @private
   */
  _convertToJimpFormat(normalizedState) {
    return {
      imageData: normalizedState.imageData,
      filters: normalizedState.filters,
      canvas: normalizedState.canvas
    };
  }

  /**
   * 转换为TUI格式
   * @param {Object} normalizedState - 标准化状态
   * @returns {Object} TUI格式状态
   * @private
   */
  _convertToTuiFormat(normalizedState) {
    return {
      imageData: normalizedState.imageData,
      canvas: normalizedState.canvas
    };
  }
}

export default StateConverter;
