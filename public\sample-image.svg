<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#gradient)"/>
  <circle cx="150" cy="150" r="80" fill="rgba(255,255,255,0.3)"/>
  <circle cx="450" cy="250" r="60" fill="rgba(255,255,255,0.2)"/>
  <rect x="200" y="200" width="200" height="100" fill="rgba(255,255,255,0.1)" rx="10"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24" fill="white" text-anchor="middle" dy=".3em">样例图片 - 用于测试图片编辑功能</text>
  <text x="50%" y="60%" font-family="Arial, sans-serif" font-size="16" fill="rgba(255,255,255,0.8)" text-anchor="middle" dy=".3em">Sample Image for Testing</text>
</svg>
