# 用户指南

欢迎使用 PhotoEditor Demo！这是一个功能强大的图像编辑演示应用，集成了多个流行的JavaScript图像编辑库。

## 📋 目录

- [快速开始](getting-started.md) - 安装和基本使用
- [使用指南](usage-guide.md) - 详细功能说明
- [浏览器兼容性](browser-compatibility.md) - 支持的浏览器

## 🌟 项目特色

### 多库集成
- **TUI Image Editor** - 完整的UI界面，开箱即用
- **Fabric.js** - 高度可定制的Canvas操作
- **Cropper.js** - 专业的图片裁剪功能
- **Jimp** - 纯JavaScript图片处理
- **Konva.js** - 高性能2D图形渲染

### 核心功能
- 🖼️ **图片裁剪** - 支持自由裁剪和按比例裁剪
- 🔄 **图片旋转** - 支持任意角度旋转
- ☀️ **亮度调节** - 实时调节图片亮度
- 🎨 **对比度调节** - 实时调节图片对比度
- 📁 **文件操作** - 加载本地图片、下载编辑结果

### 用户体验
- 📱 **响应式设计** - 适配桌面端、平板端和移动端
- 🎯 **直观操作** - 简单易用的界面设计
- ⚡ **实时预览** - 即时查看编辑效果
- 🚀 **高性能** - 优化的渲染和交互性能

## 🚀 快速开始

如果您是第一次使用，建议从这里开始：

1. **[安装和运行](getting-started.md#安装和运行)** - 获取项目并在本地运行
2. **[基本操作](getting-started.md#基本操作)** - 学习基本的图片编辑操作
3. **[选择编辑库](getting-started.md#选择编辑库)** - 了解不同库的特点和适用场景

## 📖 详细使用说明

想要深入了解各个功能，请查看：

- **[功能概览](usage-guide.md#功能概览)** - 所有功能的详细说明
- **[操作步骤](usage-guide.md#操作步骤)** - 具体的操作流程
- **[各库特色](usage-guide.md#各库特色功能)** - 每个库的独特功能

## 🔧 技术要求

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 系统要求
- 现代浏览器环境
- 建议内存 4GB+
- 建议图片大小不超过 5MB

## 💡 使用建议

### 性能优化
- 使用适当大小的图片（建议不超过5MB）
- 在移动设备上使用较小的图片
- 关闭不需要的功能以提高性能

### 最佳实践
- 在编辑前备份原始图片
- 定期保存编辑进度
- 根据需求选择合适的编辑库

## ❓ 常见问题

### Q: 支持哪些图片格式？
A: 支持常见的图片格式：JPG、PNG、GIF、SVG等。

### Q: 可以撤销编辑操作吗？
A: 大部分库支持重置功能，可以恢复到原始状态。

### Q: 移动设备上的表现如何？
A: 项目采用响应式设计，在移动设备上也有良好的体验，但建议使用较小的图片。

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. 查看 [故障排除](../troubleshooting/README.md) 文档
2. 查看 [常见问题](../troubleshooting/common-issues.md)
3. 在 GitHub 上提交 Issue

## 📚 相关文档

- [开发者指南](../developer-guide/README.md) - 适合开发者的技术文档
- [部署指南](../deployment/README.md) - 项目部署相关
- [故障排除](../troubleshooting/README.md) - 问题解决方案

---

*准备好开始了吗？让我们从 [快速开始](getting-started.md) 开始吧！*
